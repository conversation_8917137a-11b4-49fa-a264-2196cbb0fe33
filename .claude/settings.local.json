{"permissions": {"allow": ["Bash(rm:*)", "Bash(go build:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "Bash(go mod:*)", "Bash(gofmt:*)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(touch:*)", "Bash(ls:*)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(pkill:*)", "Bash(go install:*)", "<PERSON>sh(swag init:*)", "Bash(go env:*)", "Bash($HOME/go/bin/swag init:*)", "Bash(make gen-docs:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(go get:*)", "<PERSON><PERSON>(true)"], "deny": []}}